<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>开源AI Agent代码生成框架分析</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .slide-container {
            width: 1280px;
            min-height: 720px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            margin: 0 auto;
            position: relative;
            overflow: hidden;
        }
        .content-wrapper {
            padding: 35px;
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        .slide-title {
            font-size: 1.9rem;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 25px;
            text-align: center;
        }
        .frameworks-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            flex: 1;
        }
        .framework-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
            transition: transform 0.3s ease;
        }
        .framework-card:hover {
            transform: translateY(-5px);
        }
        .framework-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        .framework-logo {
            width: 35px;
            height: 35px;
            border-radius: 8px;
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            color: white;
        }
        .openhands-logo { background: #2563eb; }
        .metagpt-logo { background: #7c3aed; }
        .chatdev-logo { background: #059669; }
        .framework-name {
            font-size: 1.2rem;
            font-weight: 600;
            color: #2d3748;
        }
        .framework-subtitle {
            font-size: 0.8rem;
            color: #718096;
            margin-top: 2px;
        }
        .framework-description {
            color: #4a5568;
            font-size: 0.85rem;
            line-height: 1.5;
            margin-bottom: 15px;
            flex: 1;
        }
        .key-features {
            margin-bottom: 15px;
        }
        .features-title {
            font-size: 0.9rem;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 8px;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 6px;
            font-size: 0.8rem;
            color: #4a5568;
        }
        .feature-icon {
            width: 12px;
            height: 12px;
            margin-right: 8px;
            color: #48bb78;
        }
        .architecture-highlight {
            background: #f0fff4;
            border: 1px solid #9ae6b4;
            border-radius: 8px;
            padding: 10px;
            margin-top: auto;
        }
        .highlight-title {
            font-size: 0.8rem;
            font-weight: 600;
            color: #22543d;
            margin-bottom: 5px;
        }
        .highlight-text {
            font-size: 0.75rem;
            color: #2f855a;
            line-height: 1.4;
        }
        .comparison-table {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            margin-top: 20px;
        }
        .table-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 15px;
            text-align: center;
        }
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr;
            gap: 1px;
            background: #e2e8f0;
            border-radius: 8px;
            overflow: hidden;
        }
        .table-header {
            background: #4a5568;
            color: white;
            padding: 8px;
            font-size: 0.8rem;
            font-weight: 600;
            text-align: center;
        }
        .table-cell {
            background: white;
            padding: 8px;
            font-size: 0.75rem;
            color: #4a5568;
            text-align: center;
        }
        .framework-name-cell {
            font-weight: 600;
            color: #2d3748;
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <div class="content-wrapper">
            <h1 class="slide-title">开源力量：OpenHands、MetaGPT、ChatDev框架深度剖析</h1>
            
            <div class="frameworks-grid">
                <div class="framework-card">
                    <div class="framework-header">
                        <div class="framework-logo openhands-logo">
                            <i class="fas fa-hands-helping"></i>
                        </div>
                        <div>
                            <div class="framework-name">OpenHands</div>
                            <div class="framework-subtitle">原OpenDevin</div>
                        </div>
                    </div>
                    
                    <div class="framework-description">
                        基于CodeAct 1.0架构的自动化编程AI-Agent框架，将LLM代理行为整合到统一代码行动空间，实现零代码编程开发。
                    </div>
                    
                    <div class="key-features">
                        <div class="features-title">核心特点</div>
                        <ul class="feature-list">
                            <li class="feature-item">
                                <i class="fas fa-check-circle feature-icon"></i>
                                CodeAct 1.0架构
                            </li>
                            <li class="feature-item">
                                <i class="fas fa-check-circle feature-icon"></i>
                                统一代码行动空间
                            </li>
                            <li class="feature-item">
                                <i class="fas fa-check-circle feature-icon"></i>
                                多智能体协作
                            </li>
                            <li class="feature-item">
                                <i class="fas fa-check-circle feature-icon"></i>
                                自主性操作
                            </li>
                        </ul>
                    </div>
                    
                    <div class="architecture-highlight">
                        <div class="highlight-title">架构优势</div>
                        <div class="highlight-text">
                            Agent可像人类开发者一样执行任何操作，包括修改代码、运行命令、浏览网页、调用API。
                        </div>
                    </div>
                </div>
                
                <div class="framework-card">
                    <div class="framework-header">
                        <div class="framework-logo metagpt-logo">
                            <i class="fas fa-users-cog"></i>
                        </div>
                        <div>
                            <div class="framework-name">MetaGPT</div>
                            <div class="framework-subtitle">多智能体元编程</div>
                        </div>
                    </div>
                    
                    <div class="framework-description">
                        利用SOP（标准作业程序）协调多智能体系统，模拟虚拟软件团队，覆盖从需求分析到代码实现的全生命周期。
                    </div>
                    
                    <div class="key-features">
                        <div class="features-title">核心特点</div>
                        <ul class="feature-list">
                            <li class="feature-item">
                                <i class="fas fa-check-circle feature-icon"></i>
                                虚拟软件团队
                            </li>
                            <li class="feature-item">
                                <i class="fas fa-check-circle feature-icon"></i>
                                SOP标准流程
                            </li>
                            <li class="feature-item">
                                <i class="fas fa-check-circle feature-icon"></i>
                                角色专业化
                            </li>
                            <li class="feature-item">
                                <i class="fas fa-check-circle feature-icon"></i>
                                知识共享机制
                            </li>
                        </ul>
                    </div>
                    
                    <div class="architecture-highlight">
                        <div class="highlight-title">架构优势</div>
                        <div class="highlight-text">
                            通过产品经理、架构师、工程师等角色分工，实现精细化的代码生成和管理。
                        </div>
                    </div>
                </div>
                
                <div class="framework-card">
                    <div class="framework-header">
                        <div class="framework-logo chatdev-logo">
                            <i class="fas fa-comments-dollar"></i>
                        </div>
                        <div>
                            <div class="framework-name">ChatDev</div>
                            <div class="framework-subtitle">聊天驱动开发</div>
                        </div>
                    </div>
                    
                    <div class="framework-description">
                        基于聊天的软件开发框架，通过聊天链机制和双Agent通信设计，模拟虚拟软件公司的协作开发过程。
                    </div>
                    
                    <div class="key-features">
                        <div class="features-title">核心特点</div>
                        <ul class="feature-list">
                            <li class="feature-item">
                                <i class="fas fa-check-circle feature-icon"></i>
                                聊天链机制
                            </li>
                            <li class="feature-item">
                                <i class="fas fa-check-circle feature-icon"></i>
                                双Agent通信
                            </li>
                            <li class="feature-item">
                                <i class="fas fa-check-circle feature-icon"></i>
                                瀑布模型流程
                            </li>
                            <li class="feature-item">
                                <i class="fas fa-check-circle feature-icon"></i>
                                幻觉消除机制
                            </li>
                        </ul>
                    </div>
                    
                    <div class="architecture-highlight">
                        <div class="highlight-title">架构优势</div>
                        <div class="highlight-text">
                            通过指导者和助手Agent的多轮对话，提高代码生成的精确性和完整性。
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="comparison-table">
                <div class="table-title">框架对比分析</div>
                <div class="comparison-grid">
                    <div class="table-header">框架</div>
                    <div class="table-header">核心架构</div>
                    <div class="table-header">协作模式</div>
                    <div class="table-header">代码生成特色</div>
                    
                    <div class="table-cell framework-name-cell">OpenHands</div>
                    <div class="table-cell">CodeAct 1.0</div>
                    <div class="table-cell">多Agent协作</div>
                    <div class="table-cell">零代码编程开发</div>
                    
                    <div class="table-cell framework-name-cell">MetaGPT</div>
                    <div class="table-cell">SOP标准流程</div>
                    <div class="table-cell">虚拟软件团队</div>
                    <div class="table-cell">全生命周期覆盖</div>
                    
                    <div class="table-cell framework-name-cell">ChatDev</div>
                    <div class="table-cell">聊天链机制</div>
                    <div class="table-cell">双Agent通信</div>
                    <div class="table-cell">精确性优化</div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>

