#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HTML转PPT主程序
批量处理datasets目录下的HTML文件，生成完整的PPT演示文稿
"""

import os
import glob
import argparse
from pathlib import Path
from typing import List, Tu<PERSON>
from html2ppt_converter import H<PERSON><PERSON>ontentExtractor, SlideContent
from ppt_generator import PPTGenerator


def get_html_files(datasets_dir: str) -> List[str]:
    """获取datasets目录下的所有HTML文件，按文件名排序"""
    html_pattern = os.path.join(datasets_dir, "*.html")
    html_files = glob.glob(html_pattern)
    
    # 按文件名排序，确保PPT页面顺序正确
    html_files.sort()
    
    return html_files


def analyze_html_files(html_files: List[str]) -> None:
    """分析HTML文件的内容结构"""
    print("=" * 60)
    print("HTML文件内容分析")
    print("=" * 60)
    
    extractor = HTMLContentExtractor()
    
    for i, html_file in enumerate(html_files, 1):
        filename = os.path.basename(html_file)
        print(f"\n{i}. {filename}")
        print("-" * 40)
        
        try:
            slide_content = extractor.extract_from_html(html_file)
            
            print(f"标题: {slide_content.title}")
            print(f"副标题: {slide_content.subtitle}")
            print(f"内容类型: {slide_content.content_type}")
            print(f"元素数量: {len(slide_content.elements)}")
            
            # 显示元素详情
            if slide_content.elements:
                print("主要内容:")
                for j, element in enumerate(slide_content.elements[:3], 1):  # 只显示前3个元素
                    element_type = element.get('type', 'unknown')
                    print(f"  {j}. 类型: {element_type}")
                    
                    if element_type == 'grid_item':
                        print(f"     标题: {element.get('title', 'N/A')}")
                        print(f"     详情数: {len(element.get('details', []))}")
                    elif element_type == 'table':
                        print(f"     表头: {element.get('headers', [])}")
                        print(f"     行数: {len(element.get('rows', []))}")
                    elif element_type == 'list':
                        print(f"     项目数: {len(element.get('items', []))}")
                    elif element_type == 'workflow':
                        print(f"     步骤数: {len(element.get('steps', []))}")
                    elif element_type == 'highlight':
                        print(f"     标题: {element.get('title', 'N/A')}")
                    elif element_type == 'text':
                        content = element.get('content', '')
                        print(f"     内容: {content[:50]}...")
                
                if len(slide_content.elements) > 3:
                    print(f"  ... 还有 {len(slide_content.elements) - 3} 个元素")
            
        except Exception as e:
            print(f"错误: {str(e)}")


def convert_html_to_ppt(html_files: List[str], output_path: str) -> None:
    """将HTML文件转换为PPT"""
    print("\n" + "=" * 60)
    print("开始转换HTML到PPT")
    print("=" * 60)
    
    extractor = HTMLContentExtractor()
    generator = PPTGenerator()
    
    for i, html_file in enumerate(html_files, 1):
        filename = os.path.basename(html_file)
        print(f"\n处理第 {i}/{len(html_files)} 个文件: {filename}")
        
        try:
            # 提取HTML内容
            slide_content = extractor.extract_from_html(html_file)
            
            # 生成PPT幻灯片
            generator.add_slide_from_content(slide_content)
            
            print(f"✓ 成功处理: {slide_content.title}")
            
        except Exception as e:
            print(f"✗ 处理失败: {str(e)}")
            continue
    
    # 保存PPT文件
    try:
        generator.save(output_path)
        print(f"\n✓ PPT文件已保存: {output_path}")
        print(f"总共生成 {len(html_files)} 张幻灯片")
    except Exception as e:
        print(f"✗ 保存PPT失败: {str(e)}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="HTML转PPT转换器")
    parser.add_argument(
        "--datasets-dir", 
        default="datasets", 
        help="HTML文件所在目录 (默认: datasets)"
    )
    parser.add_argument(
        "--output", 
        default="output.pptx", 
        help="输出PPT文件名 (默认: output.pptx)"
    )
    parser.add_argument(
        "--analyze-only", 
        action="store_true", 
        help="仅分析HTML文件，不生成PPT"
    )
    
    args = parser.parse_args()
    
    # 检查datasets目录是否存在
    if not os.path.exists(args.datasets_dir):
        print(f"错误: 目录 '{args.datasets_dir}' 不存在")
        return
    
    # 获取HTML文件列表
    html_files = get_html_files(args.datasets_dir)
    
    if not html_files:
        print(f"错误: 在目录 '{args.datasets_dir}' 中未找到HTML文件")
        return
    
    print(f"找到 {len(html_files)} 个HTML文件:")
    for i, html_file in enumerate(html_files, 1):
        filename = os.path.basename(html_file)
        print(f"  {i}. {filename}")
    
    # 分析HTML文件
    analyze_html_files(html_files)
    
    # 如果只是分析模式，则退出
    if args.analyze_only:
        print("\n分析完成。使用 --analyze-only 参数时不会生成PPT文件。")
        return
    
    # 转换为PPT
    convert_html_to_ppt(html_files, args.output)


if __name__ == "__main__":
    main()
