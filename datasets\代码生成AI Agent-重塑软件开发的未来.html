<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>引言与概述</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .slide-container {
            width: 1280px;
            min-height: 720px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0 auto;
            position: relative;
            overflow: hidden;
        }
        .content-wrapper {
            position: relative;
            z-index: 2;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            padding: 60px;
        }
        .geometric-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0.1;
            z-index: 1;
        }
        .title-main {
            font-size: 3.5rem;
            font-weight: 700;
            color: white;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            line-height: 1.2;
        }
        .subtitle {
            font-size: 1.5rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 50px;
            font-weight: 300;
        }
        .highlight-box {
            background: rgba(255,255,255,0.15);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            border: 1px solid rgba(255,255,255,0.2);
            max-width: 800px;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 30px;
            margin-top: 30px;
        }
        .feature-item {
            text-align: center;
            color: white;
        }
        .feature-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            color: #fbbf24;
        }
        .feature-text {
            font-size: 1.1rem;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <svg class="geometric-bg" viewBox="0 0 1280 720">
            <defs>
                <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
                    <path d="M 40 0 L 0 0 0 40" fill="none" stroke="white" stroke-width="1"/>
                </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#grid)" />
            <circle cx="200" cy="150" r="80" fill="rgba(255,255,255,0.1)" />
            <circle cx="1100" cy="500" r="120" fill="rgba(255,255,255,0.08)" />
            <polygon points="800,50 900,150 700,150" fill="rgba(255,255,255,0.06)" />
        </svg>
        
        <div class="content-wrapper">
            <h1 class="title-main">代码生成AI Agent</h1>
            <p class="subtitle">重塑软件开发的未来</p>
            
            <div class="highlight-box">
                <div class="feature-grid">
                    <div class="feature-item">
                        <i class="fas fa-robot feature-icon"></i>
                        <div class="feature-text">智能化编程</div>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-code feature-icon"></i>
                        <div class="feature-text">自动代码生成</div>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-rocket feature-icon"></i>
                        <div class="feature-text">效率革命</div>
                    </div>
                </div>
                
                <div style="margin-top: 40px; color: rgba(255,255,255,0.9); font-size: 1.1rem; line-height: 1.6;">
                    探索AI Agent如何改变软件开发范式<br>
                    从产品应用到技术架构的全面解析
                </div>
            </div>
        </div>
    </div>
</body>
</html>

