#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PPT生成器模块
负责将提取的HTML内容转换为PowerPoint幻灯片
"""

from typing import List, Dict, Any
from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.enum.text import PP_ALIGN, MSO_ANCHOR
from pptx.dml.color import RGBColor
from pptx.enum.shapes import MSO_SHAPE
from html2ppt_converter import SlideContent


class PPTGenerator:
    """PPT生成器"""
    
    def __init__(self):
        self.presentation = Presentation()
        # 删除默认的空白幻灯片
        if len(self.presentation.slides) > 0:
            slide_id = self.presentation.slides._sldIdLst[0]
            self.presentation.part.drop_rel(slide_id.rId)
            del self.presentation.slides._sldIdLst[0]
    
    def add_slide_from_content(self, slide_content: SlideContent) -> None:
        """根据内容添加幻灯片"""
        # 选择合适的布局
        layout = self._select_layout(slide_content.content_type)
        slide = self.presentation.slides.add_slide(layout)
        
        # 添加标题
        if slide_content.title:
            self._add_title(slide, slide_content.title)
        
        # 添加副标题
        if slide_content.subtitle:
            self._add_subtitle(slide, slide_content.subtitle)
        
        # 根据内容类型添加具体内容
        if slide_content.content_type == 'grid':
            self._add_grid_content(slide, slide_content.elements)
        elif slide_content.content_type == 'table':
            self._add_table_content(slide, slide_content.elements)
        elif slide_content.content_type == 'list':
            self._add_list_content(slide, slide_content.elements)
        elif slide_content.content_type == 'workflow':
            self._add_workflow_content(slide, slide_content.elements)
        elif slide_content.content_type == 'highlight':
            self._add_highlight_content(slide, slide_content.elements)
        else:
            self._add_text_content(slide, slide_content.elements)
    
    def _select_layout(self, content_type: str):
        """选择合适的幻灯片布局"""
        # 使用空白布局，便于自定义
        return self.presentation.slide_layouts[6]  # 空白布局
    
    def _add_title(self, slide, title: str) -> None:
        """添加标题"""
        # 在幻灯片顶部添加标题文本框
        title_box = slide.shapes.add_textbox(
            Inches(0.5), Inches(0.3), Inches(12), Inches(1)
        )
        title_frame = title_box.text_frame
        title_frame.text = title
        
        # 设置标题样式
        title_paragraph = title_frame.paragraphs[0]
        title_paragraph.alignment = PP_ALIGN.CENTER
        title_font = title_paragraph.font
        title_font.size = Pt(32)
        title_font.bold = True
        title_font.color.rgb = RGBColor(45, 55, 72)  # 深灰色
    
    def _add_subtitle(self, slide, subtitle: str) -> None:
        """添加副标题"""
        subtitle_box = slide.shapes.add_textbox(
            Inches(0.5), Inches(1.5), Inches(12), Inches(0.8)
        )
        subtitle_frame = subtitle_box.text_frame
        subtitle_frame.text = subtitle
        
        # 设置副标题样式
        subtitle_paragraph = subtitle_frame.paragraphs[0]
        subtitle_paragraph.alignment = PP_ALIGN.CENTER
        subtitle_font = subtitle_paragraph.font
        subtitle_font.size = Pt(18)
        subtitle_font.color.rgb = RGBColor(74, 85, 104)  # 中灰色
    
    def _add_grid_content(self, slide, elements: List[Dict[str, Any]]) -> None:
        """添加网格内容"""
        if not elements:
            return
        
        # 计算网格布局
        num_items = len(elements)
        if num_items <= 3:
            cols = num_items
            rows = 1
        elif num_items <= 6:
            cols = 3
            rows = 2
        else:
            cols = 3
            rows = (num_items + 2) // 3
        
        # 计算每个网格项的尺寸和位置
        start_y = Inches(2.5)
        grid_width = Inches(11)
        grid_height = Inches(4)
        item_width = grid_width / cols
        item_height = grid_height / rows
        
        for i, element in enumerate(elements):
            if i >= cols * rows:
                break
                
            row = i // cols
            col = i % cols
            
            x = Inches(0.5) + col * item_width
            y = start_y + row * item_height
            
            # 添加网格项文本框
            item_box = slide.shapes.add_textbox(
                x, y, item_width - Inches(0.2), item_height - Inches(0.2)
            )
            text_frame = item_box.text_frame
            text_frame.margin_left = Inches(0.1)
            text_frame.margin_right = Inches(0.1)
            text_frame.margin_top = Inches(0.1)
            text_frame.margin_bottom = Inches(0.1)
            
            # 添加标题
            if element.get('title'):
                p = text_frame.paragraphs[0]
                p.text = element['title']
                p.font.size = Pt(16)
                p.font.bold = True
                p.font.color.rgb = RGBColor(45, 55, 72)
                
                # 添加详细内容
                if element.get('details'):
                    for detail in element['details'][:3]:  # 限制显示3个详细项
                        p = text_frame.add_paragraph()
                        p.text = f"• {detail}"
                        p.font.size = Pt(12)
                        p.font.color.rgb = RGBColor(74, 85, 104)
                        p.level = 1
    
    def _add_table_content(self, slide, elements: List[Dict[str, Any]]) -> None:
        """添加表格内容"""
        if not elements or elements[0]['type'] != 'table':
            return
        
        table_data = elements[0]
        headers = table_data.get('headers', [])
        rows = table_data.get('rows', [])
        
        if not headers or not rows:
            return
        
        # 创建表格
        table_shape = slide.shapes.add_table(
            len(rows) + 1, len(headers),
            Inches(1), Inches(2.5), Inches(11), Inches(4)
        )
        table = table_shape.table
        
        # 设置表头
        for col, header in enumerate(headers):
            cell = table.cell(0, col)
            cell.text = header
            cell.text_frame.paragraphs[0].font.bold = True
            cell.text_frame.paragraphs[0].font.size = Pt(14)
            cell.fill.solid()
            cell.fill.fore_color.rgb = RGBColor(49, 130, 206)  # 蓝色背景
            cell.text_frame.paragraphs[0].font.color.rgb = RGBColor(255, 255, 255)  # 白色文字
        
        # 填充数据行
        for row_idx, row_data in enumerate(rows):
            for col_idx, cell_data in enumerate(row_data):
                if col_idx < len(headers):
                    cell = table.cell(row_idx + 1, col_idx)
                    cell.text = str(cell_data)
                    cell.text_frame.paragraphs[0].font.size = Pt(12)
    
    def _add_list_content(self, slide, elements: List[Dict[str, Any]]) -> None:
        """添加列表内容"""
        if not elements or elements[0]['type'] != 'list':
            return
        
        list_data = elements[0]
        items = list_data.get('items', [])
        
        if not items:
            return
        
        # 创建列表文本框
        list_box = slide.shapes.add_textbox(
            Inches(1), Inches(2.5), Inches(11), Inches(4.5)
        )
        text_frame = list_box.text_frame
        
        # 添加列表项
        for i, item in enumerate(items):
            if i == 0:
                p = text_frame.paragraphs[0]
            else:
                p = text_frame.add_paragraph()
            
            p.text = f"• {item}"
            p.font.size = Pt(14)
            p.font.color.rgb = RGBColor(45, 55, 72)
            p.space_after = Pt(6)
    
    def _add_workflow_content(self, slide, elements: List[Dict[str, Any]]) -> None:
        """添加工作流程内容"""
        if not elements or elements[0]['type'] != 'workflow':
            return
        
        workflow_data = elements[0]
        steps = workflow_data.get('steps', [])
        
        if not steps:
            return
        
        # 计算步骤布局
        num_steps = len(steps)
        step_width = Inches(2.5)
        step_height = Inches(1.5)
        total_width = num_steps * step_width
        start_x = (Inches(13) - total_width) / 2
        start_y = Inches(3)
        
        for i, step in enumerate(steps):
            x = start_x + i * step_width
            
            # 添加步骤框
            step_box = slide.shapes.add_textbox(
                x, start_y, step_width - Inches(0.2), step_height
            )
            text_frame = step_box.text_frame
            text_frame.margin_left = Inches(0.1)
            text_frame.margin_right = Inches(0.1)
            
            # 添加步骤编号
            if step.get('number'):
                p = text_frame.paragraphs[0]
                p.text = f"步骤 {step['number']}"
                p.font.size = Pt(14)
                p.font.bold = True
                p.font.color.rgb = RGBColor(49, 130, 206)
                p.alignment = PP_ALIGN.CENTER
            
            # 添加步骤标题
            if step.get('title'):
                p = text_frame.add_paragraph()
                p.text = step['title']
                p.font.size = Pt(12)
                p.font.bold = True
                p.font.color.rgb = RGBColor(45, 55, 72)
                p.alignment = PP_ALIGN.CENTER
            
            # 添加步骤描述
            if step.get('description'):
                p = text_frame.add_paragraph()
                p.text = step['description']
                p.font.size = Pt(10)
                p.font.color.rgb = RGBColor(74, 85, 104)
                p.alignment = PP_ALIGN.CENTER
    
    def _add_highlight_content(self, slide, elements: List[Dict[str, Any]]) -> None:
        """添加高亮内容"""
        if not elements:
            return
        
        for i, element in enumerate(elements):
            if element['type'] != 'highlight':
                continue
            
            # 创建高亮框
            highlight_box = slide.shapes.add_textbox(
                Inches(1), Inches(2.5 + i * 1.5), Inches(11), Inches(1.2)
            )
            text_frame = highlight_box.text_frame
            
            # 添加标题
            if element.get('title'):
                p = text_frame.paragraphs[0]
                p.text = element['title']
                p.font.size = Pt(16)
                p.font.bold = True
                p.font.color.rgb = RGBColor(34, 84, 61)  # 深绿色
            
            # 添加内容
            if element.get('content'):
                p = text_frame.add_paragraph()
                p.text = element['content']
                p.font.size = Pt(12)
                p.font.color.rgb = RGBColor(47, 133, 90)  # 绿色
    
    def _add_text_content(self, slide, elements: List[Dict[str, Any]]) -> None:
        """添加普通文本内容"""
        if not elements:
            return
        
        # 创建文本框
        text_box = slide.shapes.add_textbox(
            Inches(1), Inches(2.5), Inches(11), Inches(4.5)
        )
        text_frame = text_box.text_frame
        
        # 添加文本内容
        for i, element in enumerate(elements):
            if element['type'] != 'text':
                continue
            
            if i == 0:
                p = text_frame.paragraphs[0]
            else:
                p = text_frame.add_paragraph()
            
            p.text = element['content']
            p.font.size = Pt(14)
            p.font.color.rgb = RGBColor(45, 55, 72)
            p.space_after = Pt(12)
    
    def save(self, output_path: str) -> None:
        """保存PPT文件"""
        self.presentation.save(output_path)
