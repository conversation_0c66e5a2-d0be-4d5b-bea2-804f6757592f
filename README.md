# HTML转PPT转换器

这个工具可以将HTML幻灯片文件批量转换为PowerPoint演示文稿。

## 功能特点

### 🎯 智能内容识别
- **标题提取**: 自动识别主标题和副标题
- **内容类型识别**: 基于CSS类名后缀智能识别内容类型
  - `-grid`: 网格布局内容
  - `-table`: 表格数据
  - `-list`: 列表内容
  - `-workflow`: 工作流程
  - `-card`: 卡片内容
  - `-highlight`: 高亮框内容

### 📊 多种内容类型支持
- **网格布局**: 自动排列卡片式内容
- **表格数据**: 生成格式化的PPT表格
- **列表内容**: 转换为项目符号列表
- **工作流程**: 生成步骤式流程图
- **高亮内容**: 突出显示重要信息

### 🎨 样式保持
- 保持原HTML的视觉层次
- 智能字体大小和颜色设置
- 合理的布局和间距

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 基本用法

```bash
# 转换datasets目录下的所有HTML文件为PPT
python main.py

# 指定输出文件名
python main.py --output my_presentation.pptx

# 指定HTML文件目录
python main.py --datasets-dir /path/to/html/files
```

### 分析模式

```bash
# 仅分析HTML文件结构，不生成PPT
python main.py --analyze-only
```

### 完整参数说明

```bash
python main.py --help
```

参数说明：
- `--datasets-dir`: HTML文件所在目录 (默认: datasets)
- `--output`: 输出PPT文件名 (默认: output.pptx)
- `--analyze-only`: 仅分析HTML文件，不生成PPT

## HTML文件要求

### 文件结构
HTML文件应该遵循以下结构：
```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <title>幻灯片标题</title>
    <!-- CSS样式 -->
</head>
<body>
    <div class="slide-container">
        <div class="content-wrapper">
            <h1 class="title-main">主标题</h1>
            <p class="subtitle">副标题</p>
            <!-- 其他内容 -->
        </div>
    </div>
</body>
</html>
```

### CSS类名约定
为了正确识别内容类型，请使用以下CSS类名后缀：

- **标题类**: `title-main`, `slide-title`
- **副标题**: `subtitle`
- **网格布局**: `*-grid`, `frameworks-grid`, `practices-grid`
- **表格**: `*-table`, `comparison-grid`
- **列表**: `*-list`, `feature-list`, `practice-points`
- **工作流程**: `workflow-steps`, `workflow-step`
- **卡片**: `*-card`, `framework-card`, `practice-card`
- **高亮框**: `highlight-box`, `benefits-box`, `advantage-box`

## 示例

### 1. 分析HTML文件
```bash
python main.py --analyze-only
```

输出示例：
```
找到 9 个HTML文件:
  1. OpenHands、MetaGPT、ChatDev框架深度剖析.html
  2. ds_开源框架对比.html
  3. 代码生成AI Agent-重塑软件开发的未来.html
  ...

============================================================
HTML文件内容分析
============================================================

1. OpenHands、MetaGPT、ChatDev框架深度剖析.html
----------------------------------------
标题: 开源AI Agent代码生成框架分析
副标题: 
内容类型: grid
元素数量: 3
主要内容:
  1. 类型: grid_item
     标题: OpenHands
     详情数: 4
  2. 类型: grid_item
     标题: MetaGPT
     详情数: 4
  3. 类型: grid_item
     标题: ChatDev
     详情数: 4
```

### 2. 生成PPT
```bash
python main.py --output ai_agent_presentation.pptx
```

输出示例：
```
============================================================
开始转换HTML到PPT
============================================================

处理第 1/9 个文件: OpenHands、MetaGPT、ChatDev框架深度剖析.html
✓ 成功处理: 开源AI Agent代码生成框架分析

处理第 2/9 个文件: ds_开源框架对比.html
✓ 成功处理: 多智能体框架深度剖析

...

✓ PPT文件已保存: ai_agent_presentation.pptx
总共生成 9 张幻灯片
```

## 项目结构

```
html2ppt_aug/
├── datasets/                    # HTML文件目录
│   ├── *.html                  # 待转换的HTML文件
├── html2ppt_converter.py       # HTML内容提取器
├── ppt_generator.py            # PPT生成器
├── main.py                     # 主程序
├── requirements.txt            # 依赖包列表
└── README.md                   # 说明文档
```

## 技术实现

### 核心组件

1. **HTMLContentExtractor**: HTML内容提取器
   - 使用BeautifulSoup解析HTML
   - 基于CSS类名模式匹配识别内容类型
   - 提取标题、列表、表格、网格等结构化数据

2. **PPTGenerator**: PPT生成器
   - 使用python-pptx库生成PowerPoint文件
   - 支持多种内容类型的布局
   - 自动调整字体、颜色和间距

3. **SlideContent**: 数据结构
   - 统一的幻灯片内容表示
   - 支持不同类型的内容元素

### 内容识别算法

工具使用正则表达式模式匹配来识别不同的内容类型：

```python
content_type_patterns = {
    'grid': [r'-grid$', r'frameworks-grid', r'practices-grid'],
    'table': [r'-table', r'comparison-grid', r'table-cell'],
    'list': [r'-list', r'feature-list', r'practice-points'],
    'workflow': [r'workflow-steps', r'workflow-step'],
    # ...
}
```

## 注意事项

1. **文件编码**: 确保HTML文件使用UTF-8编码
2. **CSS类名**: 遵循约定的类名后缀以获得最佳识别效果
3. **文件顺序**: HTML文件按文件名排序，确保PPT页面顺序正确
4. **内容长度**: 过长的文本内容会被自动截断以适应PPT布局

## 故障排除

### 常见问题

1. **找不到HTML文件**
   - 检查datasets目录是否存在
   - 确认HTML文件扩展名为.html

2. **内容识别不准确**
   - 检查HTML中的CSS类名是否符合约定
   - 使用`--analyze-only`参数查看识别结果

3. **PPT生成失败**
   - 确保安装了所有依赖包
   - 检查输出目录是否有写入权限

### 调试模式

使用分析模式可以查看详细的内容提取结果：
```bash
python main.py --analyze-only
```

这将显示每个HTML文件的解析结果，帮助调试内容识别问题。
