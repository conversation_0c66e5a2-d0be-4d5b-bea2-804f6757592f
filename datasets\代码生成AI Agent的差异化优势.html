<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>通用AI Agent vs 代码生成AI Agent</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .slide-container {
            width: 1280px;
            min-height: 720px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            margin: 0 auto;
            position: relative;
            overflow: hidden;
        }
        .content-wrapper {
            padding: 60px;
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        .slide-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 40px;
            text-align: center;
        }
        .comparison-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            flex: 1;
        }
        .agent-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }
        .general-agent {
            border-color: #e2e8f0;
        }
        .code-agent {
            border-color: #3182ce;
            background: linear-gradient(135deg, #ebf8ff 0%, #bee3f8 100%);
        }
        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 25px;
        }
        .card-icon {
            font-size: 2.5rem;
            margin-right: 15px;
        }
        .general-icon {
            color: #718096;
        }
        .code-icon {
            color: #3182ce;
        }
        .card-title {
            font-size: 1.8rem;
            font-weight: 600;
            color: #2d3748;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            font-size: 1rem;
            color: #4a5568;
        }
        .feature-icon {
            width: 20px;
            height: 20px;
            margin-right: 12px;
            color: #48bb78;
        }
        .advantage-box {
            background: #f0fff4;
            border: 2px solid #48bb78;
            border-radius: 15px;
            padding: 20px;
            margin-top: 20px;
        }
        .advantage-title {
            font-weight: 600;
            color: #22543d;
            margin-bottom: 10px;
            font-size: 1.1rem;
        }
        .advantage-text {
            color: #2f855a;
            font-size: 0.95rem;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <div class="content-wrapper">
            <h1 class="slide-title">从通用到专业：代码生成AI Agent的差异化优势</h1>
            
            <div class="comparison-container">
                <div class="agent-card general-agent">
                    <div class="card-header">
                        <i class="fas fa-brain card-icon general-icon"></i>
                        <h2 class="card-title">通用AI Agent</h2>
                    </div>
                    
                    <ul class="feature-list">
                        <li class="feature-item">
                            <i class="fas fa-check-circle feature-icon"></i>
                            广泛的知识覆盖面
                        </li>
                        <li class="feature-item">
                            <i class="fas fa-check-circle feature-icon"></i>
                            多领域问题解答
                        </li>
                        <li class="feature-item">
                            <i class="fas fa-check-circle feature-icon"></i>
                            通用对话能力
                        </li>
                        <li class="feature-item">
                            <i class="fas fa-check-circle feature-icon"></i>
                            基础代码理解
                        </li>
                        <li class="feature-item">
                            <i class="fas fa-times-circle" style="color: #e53e3e;"></i>
                            缺乏深度专业化
                        </li>
                        <li class="feature-item">
                            <i class="fas fa-times-circle" style="color: #e53e3e;"></i>
                            工具集成有限
                        </li>
                        <li class="feature-item">
                            <i class="fas fa-times-circle" style="color: #e53e3e;"></i>
                            上下文理解浅层
                        </li>
                    </ul>
                </div>
                
                <div class="agent-card code-agent">
                    <div class="card-header">
                        <i class="fas fa-code card-icon code-icon"></i>
                        <h2 class="card-title">代码生成AI Agent</h2>
                    </div>
                    
                    <ul class="feature-list">
                        <li class="feature-item">
                            <i class="fas fa-check-circle feature-icon"></i>
                            深度代码理解能力
                        </li>
                        <li class="feature-item">
                            <i class="fas fa-check-circle feature-icon"></i>
                            多语言编程支持
                        </li>
                        <li class="feature-item">
                            <i class="fas fa-check-circle feature-icon"></i>
                            IDE深度集成
                        </li>
                        <li class="feature-item">
                            <i class="fas fa-check-circle feature-icon"></i>
                            项目上下文感知
                        </li>
                        <li class="feature-item">
                            <i class="fas fa-check-circle feature-icon"></i>
                            自动化测试生成
                        </li>
                        <li class="feature-item">
                            <i class="fas fa-check-circle feature-icon"></i>
                            代码重构优化
                        </li>
                        <li class="feature-item">
                            <i class="fas fa-check-circle feature-icon"></i>
                            端到端开发流程
                        </li>
                    </ul>
                    
                    <div class="advantage-box">
                        <div class="advantage-title">专业化优势</div>
                        <div class="advantage-text">
                            通过领域专业化，代码生成AI Agent能够提供更精准的代码建议、更深入的项目理解和更高效的开发体验。
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>

