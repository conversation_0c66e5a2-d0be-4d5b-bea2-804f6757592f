<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>代码生成AI Agent产品应用</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .slide-container {
            width: 1280px;
            min-height: 720px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            margin: 0 auto;
            position: relative;
            overflow: hidden;
        }
        .content-wrapper {
            padding: 50px;
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        .slide-title {
            font-size: 2.2rem;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 30px;
            text-align: center;
        }
        .products-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 25px;
            margin-bottom: 30px;
        }
        .product-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border-left: 4px solid #3182ce;
            transition: transform 0.3s ease;
        }
        .product-card:hover {
            transform: translateY(-5px);
        }
        .product-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        .product-logo {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            margin-right: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }
        .cursor-logo { background: #000; }
        .github-logo { background: #24292e; }
        .alibaba-logo { background: #ff6a00; }
        .codeium-logo { background: #09b6a2; }
        .product-name {
            font-size: 1.3rem;
            font-weight: 600;
            color: #2d3748;
        }
        .product-description {
            color: #4a5568;
            font-size: 0.9rem;
            line-height: 1.5;
            margin-bottom: 15px;
        }
        .product-features {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        .feature-tag {
            background: #ebf8ff;
            color: #3182ce;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        .market-trends {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        .trends-title {
            font-size: 1.4rem;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }
        .trends-icon {
            margin-right: 10px;
            color: #48bb78;
        }
        .trends-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
        }
        .trend-item {
            text-align: center;
            padding: 15px;
            background: #f7fafc;
            border-radius: 10px;
        }
        .trend-number {
            font-size: 2rem;
            font-weight: 700;
            color: #3182ce;
            margin-bottom: 5px;
        }
        .trend-label {
            font-size: 0.9rem;
            color: #4a5568;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <div class="content-wrapper">
            <h1 class="slide-title">产品生态：代码生成AI Agent的实际应用</h1>
            
            <div class="products-grid">
                <div class="product-card">
                    <div class="product-header">
                        <div class="product-logo cursor-logo">
                            <i class="fas fa-mouse-pointer"></i>
                        </div>
                        <div class="product-name">Cursor</div>
                    </div>
                    <div class="product-description">
                        AI原生代码编辑器，提供智能代码补全、重构和调试功能，支持多种编程语言和框架。
                    </div>
                    <div class="product-features">
                        <span class="feature-tag">智能补全</span>
                        <span class="feature-tag">代码重构</span>
                        <span class="feature-tag">结对编程</span>
                    </div>
                </div>
                
                <div class="product-card">
                    <div class="product-header">
                        <div class="product-logo github-logo">
                            <i class="fab fa-github"></i>
                        </div>
                        <div class="product-name">GitHub Copilot</div>
                    </div>
                    <div class="product-description">
                        基于OpenAI Codex的AI编程助手，集成在主流IDE中，提供实时代码建议和自动补全。
                    </div>
                    <div class="product-features">
                        <span class="feature-tag">实时建议</span>
                        <span class="feature-tag">多IDE支持</span>
                        <span class="feature-tag">上下文理解</span>
                    </div>
                </div>
                
                <div class="product-card">
                    <div class="product-header">
                        <div class="product-logo alibaba-logo">
                            <i class="fas fa-code"></i>
                        </div>
                        <div class="product-name">阿里云灵码</div>
                    </div>
                    <div class="product-description">
                        阿里云推出的智能编码助手，支持自定义指令和上下文组装，提供个性化编程体验。
                    </div>
                    <div class="product-features">
                        <span class="feature-tag">自定义指令</span>
                        <span class="feature-tag">上下文组装</span>
                        <span class="feature-tag">中文优化</span>
                    </div>
                </div>
                
                <div class="product-card">
                    <div class="product-header">
                        <div class="product-logo codeium-logo">
                            <i class="fas fa-robot"></i>
                        </div>
                        <div class="product-name">Winsurf</div>
                    </div>
                    <div class="product-description">
                        基于AI Flow范式的编程助手，提供超长System Prompt和XML工具描述，支持独立或协作开发。
                    </div>
                    <div class="product-features">
                        <span class="feature-tag">AI Flow</span>
                        <span class="feature-tag">工具集成</span>
                        <span class="feature-tag">协作开发</span>
                    </div>
                </div>
            </div>
            
            <div class="market-trends">
                <h3 class="trends-title">
                    <i class="fas fa-chart-line trends-icon"></i>
                    市场发展趋势
                </h3>
                <div class="trends-grid">
                    <div class="trend-item">
                        <div class="trend-number">85%</div>
                        <div class="trend-label">开发者使用率增长</div>
                    </div>
                    <div class="trend-item">
                        <div class="trend-number">40%</div>
                        <div class="trend-label">开发效率提升</div>
                    </div>
                    <div class="trend-item">
                        <div class="trend-number">60%</div>
                        <div class="trend-label">代码质量改善</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>

