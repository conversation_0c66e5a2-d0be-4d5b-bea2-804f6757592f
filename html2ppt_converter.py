#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HTML to PPT Converter
将HTML幻灯片文件转换为PowerPoint演示文稿

主要功能：
1. 解析HTML文件，提取幻灯片内容
2. 识别不同类型的内容元素（标题、列表、表格、卡片等）
3. 将HTML内容转换为PPT幻灯片
4. 批量处理多个HTML文件生成完整PPT
"""

import os
import re
import glob
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from bs4 import BeautifulSoup, Tag
from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.enum.text import PP_ALIGN, MSO_ANCHOR
from pptx.dml.color import RGBColor
from pptx.enum.shapes import MSO_SHAPE


@dataclass
class SlideContent:
    """幻灯片内容数据结构"""
    title: str = ""
    subtitle: str = ""
    content_type: str = ""  # title, grid, table, list, workflow
    elements: List[Dict[str, Any]] = None
    background_style: str = ""
    
    def __post_init__(self):
        if self.elements is None:
            self.elements = []


class HTMLContentExtractor:
    """HTML内容提取器"""
    
    def __init__(self):
        self.content_type_patterns = {
            'title': [r'title-main', r'slide-title'],
            'subtitle': [r'subtitle'],
            'grid': [r'-grid$', r'frameworks-grid', r'practices-grid', r'feature-grid'],
            'table': [r'-table', r'comparison-grid', r'table-cell'],
            'list': [r'-list', r'feature-list', r'practice-points'],
            'workflow': [r'workflow-steps', r'workflow-step'],
            'card': [r'-card$', r'framework-card', r'practice-card'],
            'highlight': [r'highlight-box', r'benefits-box', r'advantage-box']
        }
    
    def extract_from_html(self, html_file_path: str) -> SlideContent:
        """从HTML文件提取幻灯片内容"""
        with open(html_file_path, 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        soup = BeautifulSoup(html_content, 'html.parser')
        slide_content = SlideContent()
        
        # 提取标题
        slide_content.title = self._extract_title(soup)
        slide_content.subtitle = self._extract_subtitle(soup)
        
        # 识别主要内容类型
        slide_content.content_type = self._identify_content_type(soup)
        
        # 根据内容类型提取具体元素
        slide_content.elements = self._extract_elements_by_type(soup, slide_content.content_type)
        
        # 提取背景样式信息
        slide_content.background_style = self._extract_background_style(soup)
        
        return slide_content
    
    def _extract_title(self, soup: BeautifulSoup) -> str:
        """提取主标题"""
        for pattern in self.content_type_patterns['title']:
            title_elem = soup.find(class_=re.compile(pattern))
            if title_elem:
                return title_elem.get_text(strip=True)
        
        # 备选方案：查找h1标签
        h1_elem = soup.find('h1')
        if h1_elem:
            return h1_elem.get_text(strip=True)
        
        return ""
    
    def _extract_subtitle(self, soup: BeautifulSoup) -> str:
        """提取副标题"""
        for pattern in self.content_type_patterns['subtitle']:
            subtitle_elem = soup.find(class_=re.compile(pattern))
            if subtitle_elem:
                return subtitle_elem.get_text(strip=True)
        return ""
    
    def _identify_content_type(self, soup: BeautifulSoup) -> str:
        """识别主要内容类型"""
        # 按优先级检查不同内容类型
        type_priorities = ['workflow', 'table', 'grid', 'list', 'card', 'highlight']
        
        for content_type in type_priorities:
            patterns = self.content_type_patterns.get(content_type, [])
            for pattern in patterns:
                if soup.find(class_=re.compile(pattern)):
                    return content_type
        
        return 'text'  # 默认为文本类型
    
    def _extract_elements_by_type(self, soup: BeautifulSoup, content_type: str) -> List[Dict[str, Any]]:
        """根据内容类型提取具体元素"""
        if content_type == 'grid':
            return self._extract_grid_elements(soup)
        elif content_type == 'table':
            return self._extract_table_elements(soup)
        elif content_type == 'list':
            return self._extract_list_elements(soup)
        elif content_type == 'workflow':
            return self._extract_workflow_elements(soup)
        elif content_type == 'card':
            return self._extract_card_elements(soup)
        elif content_type == 'highlight':
            return self._extract_highlight_elements(soup)
        else:
            return self._extract_text_elements(soup)
    
    def _extract_grid_elements(self, soup: BeautifulSoup) -> List[Dict[str, Any]]:
        """提取网格布局元素"""
        elements = []
        
        # 查找网格容器
        for pattern in self.content_type_patterns['grid']:
            grid_container = soup.find(class_=re.compile(pattern))
            if grid_container:
                # 提取网格项
                grid_items = grid_container.find_all(class_=re.compile(r'(item|card)'))
                for item in grid_items:
                    element = {
                        'type': 'grid_item',
                        'title': '',
                        'content': '',
                        'icon': '',
                        'details': []
                    }
                    
                    # 提取标题
                    title_elem = item.find(class_=re.compile(r'(title|name)'))
                    if title_elem:
                        element['title'] = title_elem.get_text(strip=True)
                    
                    # 提取图标
                    icon_elem = item.find('i', class_=re.compile(r'fa[s|r|l]?'))
                    if icon_elem:
                        element['icon'] = icon_elem.get('class', [])
                    
                    # 提取详细内容
                    content_elems = item.find_all(['p', 'div', 'li'], class_=re.compile(r'(text|desc|content|feature)'))
                    for content_elem in content_elems:
                        text = content_elem.get_text(strip=True)
                        if text:
                            element['details'].append(text)
                    
                    if element['title'] or element['details']:
                        elements.append(element)
                break
        
        return elements
    
    def _extract_table_elements(self, soup: BeautifulSoup) -> List[Dict[str, Any]]:
        """提取表格元素"""
        elements = []
        
        # 查找比较表格
        comparison_grid = soup.find(class_=re.compile(r'comparison-grid'))
        if comparison_grid:
            # 提取表格数据
            headers = []
            rows = []
            
            # 查找表头
            header_elems = comparison_grid.find_all(class_=re.compile(r'(header|title)'))
            for header in header_elems:
                headers.append(header.get_text(strip=True))
            
            # 查找数据行
            item_elems = comparison_grid.find_all(class_=re.compile(r'(item|cell)'))
            current_row = []
            for item in item_elems:
                text = item.get_text(strip=True)
                if text:
                    current_row.append(text)
                    if len(current_row) == len(headers):
                        rows.append(current_row)
                        current_row = []
            
            if headers and rows:
                elements.append({
                    'type': 'table',
                    'headers': headers,
                    'rows': rows
                })
        
        return elements
    
    def _extract_list_elements(self, soup: BeautifulSoup) -> List[Dict[str, Any]]:
        """提取列表元素"""
        elements = []
        
        # 查找列表容器
        for pattern in self.content_type_patterns['list']:
            list_container = soup.find(class_=re.compile(pattern))
            if list_container:
                list_items = list_container.find_all('li')
                if list_items:
                    items = []
                    for li in list_items:
                        text = li.get_text(strip=True)
                        if text:
                            items.append(text)
                    
                    if items:
                        elements.append({
                            'type': 'list',
                            'items': items
                        })
                break
        
        return elements
    
    def _extract_workflow_elements(self, soup: BeautifulSoup) -> List[Dict[str, Any]]:
        """提取工作流程元素"""
        elements = []
        
        workflow_container = soup.find(class_=re.compile(r'workflow-steps'))
        if workflow_container:
            steps = workflow_container.find_all(class_=re.compile(r'workflow-step'))
            workflow_items = []
            
            for step in steps:
                step_data = {
                    'number': '',
                    'title': '',
                    'description': ''
                }
                
                # 提取步骤编号
                number_elem = step.find(class_=re.compile(r'step-number'))
                if number_elem:
                    step_data['number'] = number_elem.get_text(strip=True)
                
                # 提取步骤标题
                title_elem = step.find(class_=re.compile(r'step-title'))
                if title_elem:
                    step_data['title'] = title_elem.get_text(strip=True)
                
                # 提取步骤描述
                desc_elem = step.find(class_=re.compile(r'step-desc'))
                if desc_elem:
                    step_data['description'] = desc_elem.get_text(strip=True)
                
                workflow_items.append(step_data)
            
            if workflow_items:
                elements.append({
                    'type': 'workflow',
                    'steps': workflow_items
                })
        
        return elements
    
    def _extract_card_elements(self, soup: BeautifulSoup) -> List[Dict[str, Any]]:
        """提取卡片元素"""
        return self._extract_grid_elements(soup)  # 卡片和网格处理方式相似
    
    def _extract_highlight_elements(self, soup: BeautifulSoup) -> List[Dict[str, Any]]:
        """提取高亮框元素"""
        elements = []
        
        for pattern in self.content_type_patterns['highlight']:
            highlight_box = soup.find(class_=re.compile(pattern))
            if highlight_box:
                element = {
                    'type': 'highlight',
                    'title': '',
                    'content': ''
                }
                
                # 提取标题
                title_elem = highlight_box.find(class_=re.compile(r'(title|header)'))
                if title_elem:
                    element['title'] = title_elem.get_text(strip=True)
                
                # 提取内容
                content_elem = highlight_box.find(class_=re.compile(r'(text|content|desc)'))
                if content_elem:
                    element['content'] = content_elem.get_text(strip=True)
                
                if element['title'] or element['content']:
                    elements.append(element)
                break
        
        return elements
    
    def _extract_text_elements(self, soup: BeautifulSoup) -> List[Dict[str, Any]]:
        """提取普通文本元素"""
        elements = []
        
        # 提取主要文本内容
        content_wrapper = soup.find(class_='content-wrapper')
        if content_wrapper:
            # 排除已处理的标题元素
            text_elements = content_wrapper.find_all(['p', 'div'], 
                                                   class_=lambda x: x and not any(
                                                       re.search(pattern, ' '.join(x) if isinstance(x, list) else x)
                                                       for patterns in self.content_type_patterns.values()
                                                       for pattern in patterns
                                                   ))
            
            for elem in text_elements:
                text = elem.get_text(strip=True)
                if text and len(text) > 10:  # 过滤太短的文本
                    elements.append({
                        'type': 'text',
                        'content': text
                    })
        
        return elements
    
    def _extract_background_style(self, soup: BeautifulSoup) -> str:
        """提取背景样式信息"""
        style_tag = soup.find('style')
        if style_tag:
            style_content = style_tag.string
            if style_content:
                # 查找背景渐变信息
                gradient_match = re.search(r'background:\s*linear-gradient\([^)]+\)', style_content)
                if gradient_match:
                    return gradient_match.group(0)
        
        return ""
