#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试依赖包是否已安装
"""

def test_dependencies():
    """测试所需的依赖包"""
    missing_packages = []
    
    try:
        import bs4
        print("✓ beautifulsoup4 已安装")
    except ImportError:
        print("✗ beautifulsoup4 未安装")
        missing_packages.append("beautifulsoup4")
    
    try:
        import pptx
        print("✓ python-pptx 已安装")
    except ImportError:
        print("✗ python-pptx 未安装")
        missing_packages.append("python-pptx")
    
    try:
        import lxml
        print("✓ lxml 已安装")
    except ImportError:
        print("✗ lxml 未安装")
        missing_packages.append("lxml")
    
    if missing_packages:
        print(f"\n需要安装以下包:")
        for pkg in missing_packages:
            print(f"  pip install {pkg}")
        return False
    else:
        print("\n✓ 所有依赖包都已安装")
        return True

if __name__ == "__main__":
    test_dependencies()
