<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多智能体框架深度剖析</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <style>
        .slide-container {
            width: 1280px;
            min-height: 720px;
            background: linear-gradient(135deg, #f0f4f8 0%, #d9e2ec 100%);
            margin: 0 auto;
            position: relative;
            overflow: hidden;
            border-radius: 20px;
            box-shadow: 0 15px 50px rgba(0, 0, 0, 0.15);
        }
        .content-wrapper {
            padding: 50px;
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        .slide-title {
            font-size: 2.5rem;
            font-weight: 800;
            color: #1a365d;
            margin-bottom: 40px;
            text-align: center;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .subtitle {
            font-size: 1.2rem;
            color: #4a5568;
            text-align: center;
            margin-top: -30px;
            margin-bottom: 40px;
            font-weight: 500;
        }
        .frameworks-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 30px;
            margin-bottom: 40px;
        }
        .framework-card {
            background: white;
            border-radius: 18px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        .framework-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }
        .meta-card { border-top: 5px solid #3182ce; }
        .chatdev-card { border-top: 5px solid #38a169; }
        .openhands-card { border-top: 5px solid #d53f8c; }
        .framework-header {
            display: flex;
            align-items: center;
            margin-bottom: 25px;
        }
        .framework-icon {
            width: 60px;
            height: 60px;
            border-radius: 15px;
            margin-right: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.8rem;
            color: white;
        }
        .meta-icon { background: #3182ce; }
        .chatdev-icon { background: #38a169; }
        .openhands-icon { background: #d53f8c; }
        .framework-name {
            font-size: 1.5rem;
            font-weight: 700;
            color: #2d3748;
        }
        .framework-subtitle {
            color: #718096;
            font-size: 0.95rem;
            margin-top: 5px;
        }
        .framework-content {
            flex-grow: 1;
        }
        .section-title {
            font-size: 1.1rem;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
        }
        .section-icon {
            margin-right: 8px;
        }
        .framework-features {
            margin-bottom: 20px;
        }
        .feature-item {
            display: flex;
            margin-bottom: 12px;
            align-items: flex-start;
        }
        .feature-badge {
            background: #ebf8ff;
            color: #3182ce;
            padding: 4px 10px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            margin-right: 10px;
            min-width: 70px;
            text-align: center;
            flex-shrink: 0;
        }
        .meta-badge { background: #ebf8ff; color: #3182ce; }
        .chatdev-badge { background: #f0fff4; color: #38a169; }
        .openhands-badge { background: #fff5f7; color: #d53f8c; }
        .feature-text {
            color: #4a5568;
            font-size: 0.92rem;
            line-height: 1.5;
        }
        .comparison-section {
            background: white;
            border-radius: 18px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
        }
        .comparison-title {
            font-size: 1.4rem;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .comparison-icon {
            margin-right: 12px;
            color: #3182ce;
        }
        .comparison-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            text-align: center;
        }
        .comparison-header {
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 15px;
            font-size: 1.1rem;
        }
        .comparison-item {
            padding: 15px;
            border-radius: 12px;
            font-size: 0.95rem;
        }
        .metric-value {
            font-weight: 700;
            font-size: 1.1rem;
            margin-bottom: 5px;
        }
        .metric-label {
            color: #718096;
            font-size: 0.85rem;
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <div class="content-wrapper">
            <h1 class="slide-title">多智能体框架深度剖析</h1>
            <div class="subtitle">OpenHands vs MetaGPT vs ChatDev 架构设计与应用场景对比</div>
            
            <div class="frameworks-grid">
                <!-- MetaGPT 框架 -->
                <div class="framework-card meta-card">
                    <div class="framework-header">
                        <div class="framework-icon meta-icon">
                            <i class="fas fa-building"></i>
                        </div>
                        <div>
                            <div class="framework-name">MetaGPT</div>
                            <div class="framework-subtitle">虚拟软件公司模拟框架</div>
                        </div>
                    </div>
                    <div class="framework-content">
                        <div class="framework-features">
                            <div class="section-title">
                                <i class="fas fa-sitemap section-icon"></i>核心架构
                            </div>
                            <div class="feature-item">
                                <span class="feature-badge meta-badge">SOP引擎</span>
                                <div class="feature-text">标准化工作流程执行器，将敏捷开发/瀑布模型编码为可执行流程</div>
                            </div>
                            <div class="feature-item">
                                <span class="feature-badge meta-badge">角色系统</span>
                                <div class="feature-text">20+专业角色（产品经理/架构师/工程师），配备领域特定提示模板</div>
                            </div>
                            <div class="feature-item">
                                <span class="feature-badge meta-badge">通信机制</span>
                                <div class="feature-text">发布-订阅模式，结构化消息优先级处理，减少冗余通信</div>
                            </div>
                        </div>
                        <div>
                            <div class="section-title">
                                <i class="fas fa-bolt section-icon"></i>性能优势
                            </div>
                            <div class="feature-item">
                                <span class="feature-badge meta-badge">效率</span>
                                <div class="feature-text">项目平均耗时8-9分钟，成本1.09美元</div>
                            </div>
                            <div class="feature-item">
                                <span class="feature-badge meta-badge">质量</span>
                                <div class="feature-text">HumanEval基准通过率提升4.2%，支持完整PRD/技术文档输出</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- ChatDev 框架 -->
                <div class="framework-card chatdev-card">
                    <div class="framework-header">
                        <div class="framework-icon chatdev-icon">
                            <i class="fas fa-comments"></i>
                        </div>
                        <div>
                            <div class="framework-name">ChatDev</div>
                            <div class="framework-subtitle">对话驱动的软件开发框架</div>
                        </div>
                    </div>
                    <div class="framework-content">
                        <div class="framework-features">
                            <div class="section-title">
                                <i class="fas fa-cogs section-icon"></i>协作机制
                            </div>
                            <div class="feature-item">
                                <span class="feature-badge chatdev-badge">瀑布模型</span>
                                <div class="feature-text">四阶段原子任务链：设计→编码→测试→文档</div>
                            </div>
                            <div class="feature-item">
                                <span class="feature-badge chatdev-badge">角色专业化</span>
                                <div class="feature-text">CEO/CTO/程序员/测试员多角色扮演，各司其职</div>
                            </div>
                            <div class="feature-item">
                                <span class="feature-badge chatdev-badge">自反思</span>
                                <div class="feature-text">"伪我"机制反馈未达标任务，闭环质量保障</div>
                            </div>
                        </div>
                        <div>
                            <div class="section-title">
                                <i class="fas fa-chart-line section-icon"></i>效能数据
                            </div>
                            <div class="feature-item">
                                <span class="feature-badge chatdev-badge">速度</span>
                                <div class="feature-text">平均开发时间&lt;7分钟，成本&lt;0.3美元</div>
                            </div>
                            <div class="feature-item">
                                <span class="feature-badge chatdev-badge">输出</span>
                                <div class="feature-text">生成131.61行代码+53.96行文档，依赖2.9个包</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- OpenHands 框架 -->
                <div class="framework-card openhands-card">
                    <div class="framework-header">
                        <div class="framework-icon openhands-icon">
                            <i class="fas fa-microchip"></i>
                        </div>
                        <div>
                            <div class="framework-name">OpenHands</div>
                            <div class="framework-subtitle">轻量级边缘计算框架</div>
                        </div>
                    </div>
                    <div class="framework-content">
                        <div class="framework-features">
                            <div class="section-title">
                                <i class="fas fa-puzzle-piece section-icon"></i>架构创新
                            </div>
                            <div class="feature-item">
                                <span class="feature-badge openhands-badge">微服务化</span>
                                <div class="feature-text">智能体拆分为独立微服务，支持树莓派级设备部署</div>
                            </div>
                            <div class="feature-item">
                                <span class="feature-badge openhands-badge">混合通信</span>
                                <div class="feature-text">局域网gRPC+广域网MQTT协议，适应多样化网络环境</div>
                            </div>
                            <div class="feature-item">
                                <span class="feature-badge openhands-badge">资源监控</span>
                                <div class="feature-text">动态调整行为适应CPU/内存/能耗限制</div>
                            </div>
                        </div>
                        <div>
                            <div class="section-title">
                                <i class="fas fa-wrench section-icon"></i>工具系统
                            </div>
                            <div class="feature-item">
                                <span class="feature-badge openhands-badge">实时性</span>
                                <div class="feature-text">硬件直接交互（GPIO/I2C），严格时间约束机制</div>
                            </div>
                            <div class="feature-item">
                                <span class="feature-badge openhands-badge">记忆管理</span>
                                <div class="feature-text">环形缓冲区+时间窗口技术处理连续数据流</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="comparison-section">
                <h3 class="comparison-title">
                    <i class="fas fa-balance-scale comparison-icon"></i>
                    框架关键指标对比
                </h3>
                <div class="comparison-grid">
                    <div class="comparison-item">
                        <div class="comparison-header">应用场景</div>
                        <div class="metric-value meta-badge">复杂软件开发</div>
                        <div class="metric-value chatdev-badge">中小型应用</div>
                        <div class="metric-value openhands-badge">边缘计算/IoT</div>
                    </div>
                    <div class="comparison-item">
                        <div class="comparison-header">开发时间</div>
                        <div class="metric-value">8-9分钟</div>
                        <div class="metric-value">&lt;7分钟</div>
                        <div class="metric-value">实时响应</div>
                    </div>
                    <div class="comparison-item">
                        <div class="comparison-header">项目成本</div>
                        <div class="metric-value">1.09美元</div>
                        <div class="metric-value">0.3美元</div>
                        <div class="metric-value">低资源消耗</div>
                    </div>
                    <div class="comparison-item">
                        <div class="comparison-header">核心创新</div>
                        <div class="metric-value">SOP工作流</div>
                        <div class="metric-value">原子对话链</div>
                        <div class="metric-value">微服务架构</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>