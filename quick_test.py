#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试脚本 - 验证修复是否成功
"""

def test_basic_imports():
    """测试基本的Python导入"""
    print("🔍 测试基本导入...")
    
    try:
        import os
        import re
        import glob
        from pathlib import Path
        from typing import List, Dict, Any
        from dataclasses import dataclass
        print("✓ 基本Python模块导入成功")
        return True
    except Exception as e:
        print(f"✗ 基本Python模块导入失败: {e}")
        return False

def test_external_packages():
    """测试外部包导入"""
    print("\n🔍 测试外部包导入...")
    
    missing_packages = []
    
    try:
        from bs4 import BeautifulSoup
        print("✓ beautifulsoup4 导入成功")
    except ImportError:
        print("✗ beautifulsoup4 未安装")
        missing_packages.append("beautifulsoup4")
    
    try:
        from pptx import Presentation
        from pptx.util import Inches, Pt
        from pptx.enum.text import PP_ALIGN
        from pptx.dml.color import RGBColor
        print("✓ python-pptx 导入成功")
    except ImportError:
        print("✗ python-pptx 未安装")
        missing_packages.append("python-pptx")
    
    if missing_packages:
        print(f"\n⚠️  需要安装以下包:")
        for pkg in missing_packages:
            print(f"   pip install {pkg}")
        return False
    
    return True

def test_project_modules():
    """测试项目模块导入"""
    print("\n🔍 测试项目模块导入...")
    
    try:
        from html2ppt_converter import HTMLContentExtractor, SlideContent
        print("✓ html2ppt_converter 导入成功")
    except Exception as e:
        print(f"✗ html2ppt_converter 导入失败: {e}")
        return False
    
    try:
        from ppt_generator import PPTGenerator
        print("✓ ppt_generator 导入成功")
    except Exception as e:
        print(f"✗ ppt_generator 导入失败: {e}")
        return False
    
    return True

def test_datasets_directory():
    """测试datasets目录"""
    print("\n🔍 测试datasets目录...")
    
    import os
    import glob
    
    if not os.path.exists("datasets"):
        print("✗ datasets目录不存在")
        return False
    
    html_files = glob.glob("datasets/*.html")
    if not html_files:
        print("✗ datasets目录中没有HTML文件")
        return False
    
    print(f"✓ 找到 {len(html_files)} 个HTML文件")
    for i, file in enumerate(html_files[:3], 1):  # 只显示前3个
        filename = os.path.basename(file)
        print(f"   {i}. {filename}")
    
    if len(html_files) > 3:
        print(f"   ... 还有 {len(html_files) - 3} 个文件")
    
    return True

def test_basic_functionality():
    """测试基本功能"""
    print("\n🔍 测试基本功能...")
    
    try:
        from html2ppt_converter import HTMLContentExtractor, SlideContent
        from ppt_generator import PPTGenerator
        
        # 创建对象
        extractor = HTMLContentExtractor()
        generator = PPTGenerator()
        print("✓ 对象创建成功")
        
        # 测试SlideContent
        slide_content = SlideContent()
        slide_content.title = "测试标题"
        slide_content.content_type = "text"
        slide_content.elements = [{"type": "text", "content": "测试内容"}]
        print("✓ SlideContent 数据结构正常")
        
        # 测试添加幻灯片（不保存）
        generator.add_slide_from_content(slide_content)
        print("✓ 添加幻灯片功能正常")
        
        return True
        
    except Exception as e:
        print(f"✗ 基本功能测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("🚀 HTML转PPT转换器 - 快速测试")
    print("=" * 60)
    
    all_passed = True
    
    # 测试基本导入
    if not test_basic_imports():
        all_passed = False
    
    # 测试外部包
    external_ok = test_external_packages()
    if not external_ok:
        all_passed = False
        print("\n⚠️  外部包未安装，但可以继续测试项目结构...")
    
    # 测试datasets目录
    if not test_datasets_directory():
        all_passed = False
    
    # 如果外部包可用，测试项目模块和功能
    if external_ok:
        if not test_project_modules():
            all_passed = False
        else:
            if not test_basic_functionality():
                all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 所有测试通过！")
        print("\n✅ 可以运行以下命令:")
        print("   python main.py --analyze-only  # 分析HTML文件")
        print("   python main.py --output test.pptx  # 生成PPT")
    else:
        print("❌ 部分测试失败")
        if not external_ok:
            print("\n📦 请先安装依赖包:")
            print("   pip install beautifulsoup4 python-pptx lxml")
        print("\n🔧 然后重新运行测试:")
        print("   python quick_test.py")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
