#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试脚本 - 不依赖外部包的基本HTML解析测试
"""

import os
import re
import glob
from pathlib import Path


def simple_html_analysis():
    """简单的HTML文件分析，不使用BeautifulSoup"""
    datasets_dir = "datasets"
    
    if not os.path.exists(datasets_dir):
        print(f"错误: 目录 '{datasets_dir}' 不存在")
        return
    
    # 获取HTML文件列表
    html_pattern = os.path.join(datasets_dir, "*.html")
    html_files = glob.glob(html_pattern)
    html_files.sort()
    
    if not html_files:
        print(f"错误: 在目录 '{datasets_dir}' 中未找到HTML文件")
        return
    
    print(f"找到 {len(html_files)} 个HTML文件:")
    for i, html_file in enumerate(html_files, 1):
        filename = os.path.basename(html_file)
        print(f"  {i}. {filename}")
    
    print("\n" + "=" * 60)
    print("HTML文件基本分析")
    print("=" * 60)
    
    for i, html_file in enumerate(html_files, 1):
        filename = os.path.basename(html_file)
        print(f"\n{i}. {filename}")
        print("-" * 40)
        
        try:
            with open(html_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 提取标题
            title_patterns = [
                r'<h1[^>]*class="[^"]*title-main[^"]*"[^>]*>([^<]+)</h1>',
                r'<h1[^>]*class="[^"]*slide-title[^"]*"[^>]*>([^<]+)</h1>',
                r'<h1[^>]*>([^<]+)</h1>',
                r'<title>([^<]+)</title>'
            ]
            
            title = ""
            for pattern in title_patterns:
                match = re.search(pattern, content, re.IGNORECASE)
                if match:
                    title = match.group(1).strip()
                    break
            
            # 提取副标题
            subtitle_patterns = [
                r'<[^>]*class="[^"]*subtitle[^"]*"[^>]*>([^<]+)</[^>]*>',
                r'<p[^>]*class="[^"]*subtitle[^"]*"[^>]*>([^<]+)</p>'
            ]
            
            subtitle = ""
            for pattern in subtitle_patterns:
                match = re.search(pattern, content, re.IGNORECASE)
                if match:
                    subtitle = match.group(1).strip()
                    break
            
            # 识别内容类型
            content_type = "text"  # 默认
            
            type_patterns = {
                'workflow': [r'workflow-steps', r'workflow-step'],
                'table': [r'comparison-grid', r'table-cell'],
                'grid': [r'frameworks-grid', r'practices-grid', r'feature-grid', r'-grid'],
                'list': [r'feature-list', r'practice-points', r'-list'],
                'card': [r'framework-card', r'practice-card', r'-card'],
                'highlight': [r'highlight-box', r'benefits-box', r'advantage-box']
            }
            
            for ctype, patterns in type_patterns.items():
                for pattern in patterns:
                    if re.search(f'class="[^"]*{pattern}[^"]*"', content, re.IGNORECASE):
                        content_type = ctype
                        break
                if content_type != "text":
                    break
            
            # 统计一些基本信息
            div_count = len(re.findall(r'<div[^>]*>', content))
            class_count = len(re.findall(r'class="[^"]*"', content))
            
            print(f"标题: {title}")
            print(f"副标题: {subtitle}")
            print(f"内容类型: {content_type}")
            print(f"DIV元素数量: {div_count}")
            print(f"CSS类数量: {class_count}")
            print(f"文件大小: {len(content)} 字符")
            
            # 查找特定的内容元素
            if content_type == 'grid':
                grid_items = re.findall(r'<div[^>]*class="[^"]*(?:item|card)[^"]*"[^>]*>', content)
                print(f"网格项数量: {len(grid_items)}")
            
            elif content_type == 'workflow':
                workflow_steps = re.findall(r'<div[^>]*class="[^"]*workflow-step[^"]*"[^>]*>', content)
                print(f"工作流步骤数量: {len(workflow_steps)}")
            
            elif content_type == 'table':
                table_cells = re.findall(r'<div[^>]*class="[^"]*(?:item|cell)[^"]*"[^>]*>', content)
                print(f"表格单元格数量: {len(table_cells)}")
            
            elif content_type == 'list':
                list_items = re.findall(r'<li[^>]*>', content)
                print(f"列表项数量: {len(list_items)}")
            
        except Exception as e:
            print(f"错误: {str(e)}")
    
    print("\n" + "=" * 60)
    print("分析完成")
    print("=" * 60)
    print("\n建议:")
    print("1. 如果要生成PPT，请先安装依赖包:")
    print("   pip install beautifulsoup4 python-pptx lxml")
    print("2. 然后运行完整的转换程序:")
    print("   python main.py --analyze-only  # 详细分析")
    print("   python main.py                 # 生成PPT")


if __name__ == "__main__":
    simple_html_analysis()
