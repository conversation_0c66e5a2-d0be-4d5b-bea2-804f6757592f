#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复确认脚本 - 验证导入错误是否已修复
"""

def test_fixed_imports():
    """测试修复后的导入"""
    print("🔧 测试修复后的导入...")
    
    try:
        print("1. 测试 ppt_generator 导入...")
        from ppt_generator import PPTGenerator
        print("   ✓ ppt_generator 导入成功 - 修复生效！")
        
        print("2. 测试 html2ppt_converter 导入...")
        from html2ppt_converter import HTMLContentExtractor, SlideContent
        print("   ✓ html2ppt_converter 导入成功")
        
        print("3. 测试 main 模块导入...")
        import main
        print("   ✓ main 模块导入成功")
        
        return True
        
    except Exception as e:
        print(f"   ✗ 导入失败: {e}")
        return False

def test_object_creation():
    """测试对象创建"""
    print("\n🏗️  测试对象创建...")
    
    try:
        from html2ppt_converter import HTMLContentExtractor, SlideContent
        from ppt_generator import PPTGenerator
        
        # 创建提取器
        extractor = HTMLContentExtractor()
        print("   ✓ HTMLContentExtractor 创建成功")
        
        # 创建生成器
        generator = PPTGenerator()
        print("   ✓ PPTGenerator 创建成功")
        
        # 创建数据结构
        slide_content = SlideContent()
        slide_content.title = "测试标题"
        slide_content.content_type = "text"
        slide_content.elements = [{"type": "text", "content": "测试内容"}]
        print("   ✓ SlideContent 数据结构创建成功")
        
        return True
        
    except Exception as e:
        print(f"   ✗ 对象创建失败: {e}")
        return False

def test_basic_functionality():
    """测试基本功能"""
    print("\n⚙️  测试基本功能...")
    
    try:
        from html2ppt_converter import HTMLContentExtractor, SlideContent
        from ppt_generator import PPTGenerator
        
        # 创建对象
        extractor = HTMLContentExtractor()
        generator = PPTGenerator()
        
        # 创建测试内容
        slide_content = SlideContent()
        slide_content.title = "测试幻灯片"
        slide_content.subtitle = "测试副标题"
        slide_content.content_type = "text"
        slide_content.elements = [
            {"type": "text", "content": "这是一个测试文本内容"}
        ]
        
        # 测试添加幻灯片
        generator.add_slide_from_content(slide_content)
        print("   ✓ 添加幻灯片功能正常")
        
        # 检查幻灯片数量
        slide_count = len(generator.presentation.slides)
        print(f"   ✓ 当前幻灯片数量: {slide_count}")
        
        return True
        
    except Exception as e:
        print(f"   ✗ 基本功能测试失败: {e}")
        return False

def check_datasets():
    """检查datasets目录"""
    print("\n📁 检查datasets目录...")
    
    import os
    import glob
    
    if not os.path.exists("datasets"):
        print("   ⚠️  datasets目录不存在")
        return False
    
    html_files = glob.glob("datasets/*.html")
    if not html_files:
        print("   ⚠️  datasets目录中没有HTML文件")
        return False
    
    print(f"   ✓ 找到 {len(html_files)} 个HTML文件")
    return True

def main():
    """主测试函数"""
    print("=" * 60)
    print("🚀 HTML转PPT转换器 - 修复确认测试")
    print("=" * 60)
    
    all_passed = True
    
    # 测试导入
    if not test_fixed_imports():
        all_passed = False
        print("\n❌ 导入测试失败 - 可能需要安装依赖包")
        print("   运行: pip install beautifulsoup4 python-pptx lxml")
        return
    
    # 测试对象创建
    if not test_object_creation():
        all_passed = False
    
    # 测试基本功能
    if not test_basic_functionality():
        all_passed = False
    
    # 检查数据目录
    datasets_ok = check_datasets()
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 修复成功！所有测试通过！")
        print("\n✅ 现在可以正常运行:")
        if datasets_ok:
            print("   python main.py --analyze-only  # 分析HTML文件")
            print("   python main.py --output ai_agent_presentation.pptx  # 生成PPT")
        else:
            print("   ⚠️  请确保datasets目录中有HTML文件")
        
        print("\n🔧 原始错误已修复:")
        print("   ✓ 删除了错误的 'from pptx.shapes.table import Table' 导入")
        print("   ✓ 保留了必要的 pptx 模块导入")
        print("   ✓ 所有功能模块正常工作")
        
    else:
        print("❌ 部分测试失败")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
