#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试导入是否正常
"""

def test_imports():
    """测试所有模块的导入"""
    print("测试模块导入...")
    
    try:
        print("1. 测试 html2ppt_converter...")
        from html2ppt_converter import HTMLContentExtractor, SlideContent
        print("   ✓ html2ppt_converter 导入成功")
    except Exception as e:
        print(f"   ✗ html2ppt_converter 导入失败: {e}")
        return False
    
    try:
        print("2. 测试 ppt_generator...")
        from ppt_generator import PPTGenerator
        print("   ✓ ppt_generator 导入成功")
    except Exception as e:
        print(f"   ✗ ppt_generator 导入失败: {e}")
        return False
    
    try:
        print("3. 测试 main...")
        import main
        print("   ✓ main 导入成功")
    except Exception as e:
        print(f"   ✗ main 导入失败: {e}")
        return False
    
    print("\n✓ 所有模块导入成功！")
    return True

def test_basic_functionality():
    """测试基本功能"""
    print("\n测试基本功能...")
    
    try:
        from html2ppt_converter import HTMLContentExtractor, SlideContent
        from ppt_generator import PPTGenerator
        
        # 测试创建对象
        extractor = HTMLContentExtractor()
        generator = PPTGenerator()
        
        print("✓ 对象创建成功")
        
        # 测试SlideContent数据结构
        slide_content = SlideContent()
        slide_content.title = "测试标题"
        slide_content.content_type = "text"
        slide_content.elements = [{"type": "text", "content": "测试内容"}]
        
        print("✓ SlideContent 数据结构正常")
        
        # 测试添加幻灯片
        generator.add_slide_from_content(slide_content)
        print("✓ 添加幻灯片功能正常")
        
        print("\n✓ 基本功能测试通过！")
        return True
        
    except Exception as e:
        print(f"✗ 基本功能测试失败: {e}")
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("HTML转PPT转换器 - 导入测试")
    print("=" * 50)
    
    if test_imports():
        if test_basic_functionality():
            print("\n🎉 所有测试通过！可以正常运行主程序了。")
            print("\n建议运行:")
            print("  python main.py --analyze-only  # 分析HTML文件")
            print("  python main.py --output test.pptx  # 生成PPT")
        else:
            print("\n❌ 基本功能测试失败")
    else:
        print("\n❌ 模块导入失败，请检查依赖包是否安装")
        print("运行: pip install beautifulsoup4 python-pptx lxml")
