<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>代码生成AI Agent设计模式</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .slide-container {
            width: 1280px;
            min-height: 720px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            margin: 0 auto;
            position: relative;
            overflow: hidden;
        }
        .content-wrapper {
            padding: 40px;
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        .slide-title {
            font-size: 2rem;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 25px;
            text-align: center;
        }
        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            flex: 1;
        }
        .patterns-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        .section-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }
        .section-icon {
            margin-right: 10px;
            color: #3182ce;
        }
        .pattern-item {
            background: #f7fafc;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            border-left: 4px solid #3182ce;
        }
        .pattern-name {
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 8px;
            font-size: 1rem;
        }
        .pattern-desc {
            color: #4a5568;
            font-size: 0.85rem;
            line-height: 1.4;
        }
        .commonalities-list {
            list-style: none;
            padding: 0;
        }
        .commonality-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 12px;
            font-size: 0.9rem;
            color: #4a5568;
        }
        .commonality-icon {
            color: #48bb78;
            margin-right: 10px;
            margin-top: 2px;
            font-size: 0.8rem;
        }
        .differences-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 15px;
        }
        .diff-card {
            background: #ebf8ff;
            border-radius: 8px;
            padding: 12px;
            border: 1px solid #bee3f8;
        }
        .diff-title {
            font-weight: 600;
            color: #2b6cb0;
            font-size: 0.9rem;
            margin-bottom: 6px;
        }
        .diff-text {
            color: #2c5282;
            font-size: 0.8rem;
            line-height: 1.3;
        }
        .system-prompt-section {
            background: #1a202c;
            color: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        .prompt-title {
            color: #63b3ed;
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }
        .prompt-component {
            background: #2d3748;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 12px;
            border-left: 3px solid #63b3ed;
        }
        .component-name {
            color: #90cdf4;
            font-weight: 600;
            font-size: 0.9rem;
            margin-bottom: 6px;
        }
        .component-desc {
            color: #cbd5e0;
            font-size: 0.8rem;
            line-height: 1.4;
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <div class="content-wrapper">
            <h1 class="slide-title">设计模式解析：Cursor、Trae、灵码、Winsurf的核心架构</h1>
            
            <div class="main-content">
                <div class="patterns-section">
                    <h3 class="section-title">
                        <i class="fas fa-cogs section-icon"></i>
                        共性特征
                    </h3>
                    
                    <ul class="commonalities-list">
                        <li class="commonality-item">
                            <i class="fas fa-check-circle commonality-icon"></i>
                            基于大型语言模型(LLM)驱动核心决策
                        </li>
                        <li class="commonality-item">
                            <i class="fas fa-check-circle commonality-icon"></i>
                            System Prompt定义Agent角色和行为约束
                        </li>
                        <li class="commonality-item">
                            <i class="fas fa-check-circle commonality-icon"></i>
                            工具调用(Tool Use)扩展能力边界
                        </li>
                        <li class="commonality-item">
                            <i class="fas fa-check-circle commonality-icon"></i>
                            上下文感知提升决策精准度
                        </li>
                        <li class="commonality-item">
                            <i class="fas fa-check-circle commonality-icon"></i>
                            自动化减少重复性开发工作
                        </li>
                        <li class="commonality-item">
                            <i class="fas fa-check-circle commonality-icon"></i>
                            协作性支持人机或多Agent协同
                        </li>
                    </ul>
                    
                    <h3 class="section-title" style="margin-top: 25px;">
                        <i class="fas fa-puzzle-piece section-icon"></i>
                        差异化特性
                    </h3>
                    
                    <div class="differences-grid">
                        <div class="diff-card">
                            <div class="diff-title">Cursor</div>
                            <div class="diff-text">结对编程模式，详细约束规范，避免直接代码输出</div>
                        </div>
                        <div class="diff-card">
                            <div class="diff-title">Trae</div>
                            <div class="diff-text">多Agent协作，动态迭代工作流，专注Prompt优化</div>
                        </div>
                        <div class="diff-card">
                            <div class="diff-title">灵码</div>
                            <div class="diff-text">自定义指令，灵活上下文组装，对话历史引入</div>
                        </div>
                        <div class="diff-card">
                            <div class="diff-title">Winsurf</div>
                            <div class="diff-text">超长System Prompt，AI Flow范式，XML工具嵌入</div>
                        </div>
                    </div>
                </div>
                
                <div class="system-prompt-section">
                    <h3 class="prompt-title">
                        <i class="fas fa-terminal section-icon"></i>
                        System Prompt核心组件
                    </h3>
                    
                    <div class="prompt-component">
                        <div class="component-name">角色定义 (Role Definition)</div>
                        <div class="component-desc">明确Agent身份、能力范围和专业领域</div>
                    </div>
                    
                    <div class="prompt-component">
                        <div class="component-name">任务描述 (Task Description)</div>
                        <div class="component-desc">说明Agent的目标任务和工作方式</div>
                    </div>
                    
                    <div class="prompt-component">
                        <div class="component-name">通信约束 (Communication Constraints)</div>
                        <div class="component-desc">规定与用户交流的方式和格式要求</div>
                    </div>
                    
                    <div class="prompt-component">
                        <div class="component-name">工具调用约束 (Tool Calling Constraints)</div>
                        <div class="component-desc">明确工具使用规则和调用时机</div>
                    </div>
                    
                    <div class="prompt-component">
                        <div class="component-name">代码修改约束 (Code Change Constraints)</div>
                        <div class="component-desc">规定代码生成和修改的具体要求</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>

