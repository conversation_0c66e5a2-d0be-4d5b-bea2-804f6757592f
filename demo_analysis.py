#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HTML转PPT工具演示分析
展示HTML文件的解析结果和转换策略
"""

import os
import re
import glob


def demo_html_analysis():
    """演示HTML文件分析功能"""
    print("=" * 70)
    print("HTML转PPT转换器 - 深度分析演示")
    print("=" * 70)
    
    datasets_dir = "datasets"
    
    if not os.path.exists(datasets_dir):
        print(f"错误: 目录 '{datasets_dir}' 不存在")
        return
    
    # 获取HTML文件列表
    html_pattern = os.path.join(datasets_dir, "*.html")
    html_files = glob.glob(html_pattern)
    html_files.sort()
    
    if not html_files:
        print(f"错误: 在目录 '{datasets_dir}' 中未找到HTML文件")
        return
    
    print(f"\n📁 发现 {len(html_files)} 个HTML文件:")
    for i, html_file in enumerate(html_files, 1):
        filename = os.path.basename(html_file)
        print(f"  {i:2d}. {filename}")
    
    print("\n" + "=" * 70)
    print("🔍 详细内容分析")
    print("=" * 70)
    
    # 分析每个文件
    for i, html_file in enumerate(html_files, 1):
        filename = os.path.basename(html_file)
        print(f"\n📄 [{i}/{len(html_files)}] {filename}")
        print("-" * 50)
        
        try:
            with open(html_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 提取标题
            title = extract_title(content)
            subtitle = extract_subtitle(content)
            content_type = identify_content_type(content)
            
            print(f"📝 标题: {title}")
            print(f"📝 副标题: {subtitle}")
            print(f"🏷️  内容类型: {content_type}")
            
            # 分析具体内容
            analyze_content_details(content, content_type)
            
            # 转换策略建议
            print(f"🎯 转换策略: {get_conversion_strategy(content_type)}")
            
        except Exception as e:
            print(f"❌ 错误: {str(e)}")
    
    print("\n" + "=" * 70)
    print("📊 总结报告")
    print("=" * 70)
    
    # 统计不同内容类型
    type_stats = {}
    for html_file in html_files:
        try:
            with open(html_file, 'r', encoding='utf-8') as f:
                content = f.read()
            content_type = identify_content_type(content)
            type_stats[content_type] = type_stats.get(content_type, 0) + 1
        except:
            pass
    
    print("📈 内容类型分布:")
    for ctype, count in type_stats.items():
        print(f"  • {ctype}: {count} 个文件")
    
    print(f"\n🎯 转换建议:")
    print(f"  • 总共将生成 {len(html_files)} 张PPT幻灯片")
    print(f"  • 支持多种内容类型的智能布局")
    print(f"  • 保持原始设计的视觉层次")
    
    print(f"\n🚀 使用方法:")
    print(f"  1. 安装依赖: pip install beautifulsoup4 python-pptx lxml")
    print(f"  2. 分析模式: python main.py --analyze-only")
    print(f"  3. 生成PPT: python main.py --output presentation.pptx")


def extract_title(content):
    """提取标题"""
    patterns = [
        r'<h1[^>]*class="[^"]*title-main[^"]*"[^>]*>([^<]+)</h1>',
        r'<h1[^>]*class="[^"]*slide-title[^"]*"[^>]*>([^<]+)</h1>',
        r'<h1[^>]*>([^<]+)</h1>',
        r'<title>([^<]+)</title>'
    ]
    
    for pattern in patterns:
        match = re.search(pattern, content, re.IGNORECASE)
        if match:
            return match.group(1).strip()
    return "未找到标题"


def extract_subtitle(content):
    """提取副标题"""
    patterns = [
        r'<[^>]*class="[^"]*subtitle[^"]*"[^>]*>([^<]+)</[^>]*>',
        r'<p[^>]*class="[^"]*subtitle[^"]*"[^>]*>([^<]+)</p>'
    ]
    
    for pattern in patterns:
        match = re.search(pattern, content, re.IGNORECASE)
        if match:
            return match.group(1).strip()
    return ""


def identify_content_type(content):
    """识别内容类型"""
    type_patterns = {
        'workflow': [r'workflow-steps', r'workflow-step'],
        'table': [r'comparison-grid', r'table-cell'],
        'grid': [r'frameworks-grid', r'practices-grid', r'feature-grid'],
        'list': [r'feature-list', r'practice-points'],
        'card': [r'framework-card', r'practice-card'],
        'highlight': [r'highlight-box', r'benefits-box', r'advantage-box']
    }
    
    for ctype, patterns in type_patterns.items():
        for pattern in patterns:
            if re.search(f'class="[^"]*{pattern}[^"]*"', content, re.IGNORECASE):
                return ctype
    return 'text'


def analyze_content_details(content, content_type):
    """分析内容详情"""
    if content_type == 'grid':
        grid_items = re.findall(r'<div[^>]*class="[^"]*(?:framework-card|practice-card|feature-item)[^"]*"', content)
        print(f"🔲 网格项数量: {len(grid_items)}")
        
        # 提取网格项标题
        titles = re.findall(r'<div[^>]*class="[^"]*(?:framework-name|practice-title|feature-text)[^"]*"[^>]*>([^<]+)</div>', content)
        if titles:
            print(f"📋 主要项目: {', '.join(titles[:3])}{'...' if len(titles) > 3 else ''}")
    
    elif content_type == 'workflow':
        steps = re.findall(r'<div[^>]*class="[^"]*workflow-step[^"]*"', content)
        print(f"🔄 工作流步骤: {len(steps)} 个")
        
        step_titles = re.findall(r'<div[^>]*class="[^"]*step-title[^"]*"[^>]*>([^<]+)</div>', content)
        if step_titles:
            print(f"📝 步骤: {' → '.join(step_titles)}")
    
    elif content_type == 'table':
        headers = re.findall(r'<div[^>]*class="[^"]*comparison-header[^"]*"[^>]*>([^<]+)</div>', content)
        items = re.findall(r'<div[^>]*class="[^"]*comparison-item[^"]*"', content)
        print(f"📊 表格: {len(headers)} 列 × {len(items)//len(headers) if headers else 0} 行")
        if headers:
            print(f"📋 列标题: {', '.join(headers)}")
    
    elif content_type == 'list':
        list_items = re.findall(r'<li[^>]*>', content)
        print(f"📝 列表项: {len(list_items)} 个")
    
    elif content_type == 'highlight':
        highlights = re.findall(r'<div[^>]*class="[^"]*(?:highlight-box|benefits-box|advantage-box)[^"]*"', content)
        print(f"💡 高亮框: {len(highlights)} 个")
    
    else:
        paragraphs = re.findall(r'<p[^>]*>', content)
        divs = re.findall(r'<div[^>]*>', content)
        print(f"📄 文本内容: {len(paragraphs)} 段落, {len(divs)} 区块")


def get_conversion_strategy(content_type):
    """获取转换策略"""
    strategies = {
        'grid': '网格布局 → PPT多列卡片排列',
        'workflow': '工作流程 → PPT步骤图表',
        'table': '比较表格 → PPT表格组件',
        'list': '列表内容 → PPT项目符号列表',
        'card': '卡片布局 → PPT信息卡片',
        'highlight': '高亮内容 → PPT强调文本框',
        'text': '普通文本 → PPT文本内容'
    }
    return strategies.get(content_type, '自动识别布局')


if __name__ == "__main__":
    demo_html_analysis()
